[WARN  9/27/2025 1:49:24 PM] ...data/lazy/mason.nvim/lua/mason-registry/sources/init.lua:148: Ignoring duplicate registry entry "github:mason-org/mason-registry" (duplicate of "github:mason-org/mason-registry")
[INFO  9/27/2025 1:49:30 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=pyright) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:49:30 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=lua-language-server) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:49:30 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=rust-analyzer) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:49:30 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=html-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:49:30 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=css-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:49:30 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=elixir-ls) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:49:30 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=gopls) {
  debug = false,
  force = false,
  strict = false
}
[ERROR 9/27/2025 1:49:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=css-lsp) error="Installation was aborted."
[ERROR 9/27/2025 1:49:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=elixir-ls) error="Installation was aborted."
[ERROR 9/27/2025 1:49:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=gopls) error="Installation was aborted."
[ERROR 9/27/2025 1:49:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=rust-analyzer) error="Installation was aborted."
[ERROR 9/27/2025 1:49:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=lua-language-server) error="Installation was aborted."
[ERROR 9/27/2025 1:49:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=pyright) error="Installation was aborted."
[ERROR 9/27/2025 1:49:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=html-lsp) error="Installation was aborted."
[INFO  9/27/2025 1:50:15 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=pyright) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:50:16 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=lua-language-server) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:50:16 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=rust-analyzer) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:50:16 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=html-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:50:16 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=css-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:50:17 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=elixir-ls) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:50:17 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=gopls) {
  debug = false,
  force = false,
  strict = false
}
[ERROR 9/27/2025 1:50:19 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=css-lsp) error="Installation was aborted."
[ERROR 9/27/2025 1:50:19 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=elixir-ls) error="Installation was aborted."
[ERROR 9/27/2025 1:50:19 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=gopls) error="Installation was aborted."
[ERROR 9/27/2025 1:50:19 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=pyright) error="Installation was aborted."
[ERROR 9/27/2025 1:50:19 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=html-lsp) error="Installation was aborted."
[ERROR 9/27/2025 1:50:19 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=rust-analyzer) error="Installation was aborted."
[ERROR 9/27/2025 1:50:19 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=lua-language-server) error="Installation was aborted."
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=typescript-language-server) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=pyright) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=lua-language-server) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=rust-analyzer) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=html-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=css-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=elixir-ls) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:27 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=gopls) {
  debug = false,
  force = false,
  strict = false
}
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=html-lsp) error="Installation was aborted."
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=css-lsp) error="Installation was aborted."
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=elixir-ls) error="Installation was aborted."
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=gopls) error="Installation was aborted."
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=typescript-language-server) error="Installation was aborted."
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=pyright) error="Installation was aborted."
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=lua-language-server) error="Installation was aborted."
[ERROR 9/27/2025 1:51:28 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=rust-analyzer) error="Installation was aborted."
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=typescript-language-server) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=pyright) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=lua-language-server) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=rust-analyzer) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=html-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=css-lsp) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=elixir-ls) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:29 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:40: Executing installer for Package(name=gopls) {
  debug = false,
  force = false,
  strict = false
}
[INFO  9/27/2025 1:51:31 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:86: Installation succeeded for Package(name=rust-analyzer)
[INFO  9/27/2025 1:51:32 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:86: Installation succeeded for Package(name=typescript-language-server)
[INFO  9/27/2025 1:51:32 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:86: Installation succeeded for Package(name=lua-language-server)
[INFO  9/27/2025 1:51:33 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:86: Installation succeeded for Package(name=elixir-ls)
[ERROR 9/27/2025 1:51:33 PM] ...cal/nvim-data/lazy/mason.nvim/lua/mason-core/process.lua:226: Failed to spawn process. cmd="go", err="ENOENT: no such file or directory"
[ERROR 9/27/2025 1:51:33 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:93: Installation failed for Package(name=gopls) error=spawn: go failed with exit code - and signal -. Could not find executable "go" in PATH.

[INFO  9/27/2025 1:51:37 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:86: Installation succeeded for Package(name=pyright)
[INFO  9/27/2025 1:51:37 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:86: Installation succeeded for Package(name=html-lsp)
[INFO  9/27/2025 1:51:37 PM] ...zy/mason.nvim/lua/mason-core/installer/InstallRunner.lua:86: Installation succeeded for Package(name=css-lsp)