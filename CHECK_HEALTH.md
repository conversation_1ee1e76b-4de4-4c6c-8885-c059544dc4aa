
==============================================================================
lazy:                                                               2 ⚠️  1 ❌

lazy.nvim ~
- {lazy.nvim} version `11.17.1`
- ✅ OK {git} `version 2.45.1.windows.1`
- ✅ OK no existing packages found by other package managers
- ✅ OK packer_compiled.lua not found

luarocks ~
- checking `hererocks` installation
- ✅ OK no plugins require `luarocks`, so you can ignore any warnings below
- ✅ OK {python} `Python 3.13.7`
- ❌ ERROR {C:/Users/<USER>/AppData/Local/nvim-data/lazy-rocks/hererocks/bin/luarocks} not installed
- ⚠️ WARNING {C:/Users/<USER>/AppData/Local/nvim-data/lazy-rocks/hererocks/bin/lua} version `5.1` not installed
- ⚠️ WARNING Lazy won't be able to install plugins that require `luarocks`.
  Here's what you can do:
   - fix your `luarocks` installation
   - disable *hererocks* with `opts.rocks.hererocks = false`
   - disable `luarocks` support completely with `opts.rocks.enabled = false`

==============================================================================
telescope:                                                          2 ⚠️  1 ❌

Checking for required plugins ~
- ✅ OK plenary installed.
- ⚠️ WARNING nvim-treesitter not found. (Required for `:Telescope treesitter`.)

Checking external dependencies ~
- ❌ ERROR rg: not found. `live-grep` finder will not function without [BurntSushi/ripgrep](https://github.com/BurntSushi/ripgrep) installed.
- ⚠️ WARNING fd: not found. Install [sharkdp/fd](https://github.com/sharkdp/fd) for extended capabilities

===== Installed extensions ===== ~

==============================================================================
vim.deprecated:                                                             ✅

- ✅ OK No deprecated functions detected

==============================================================================
vim.health:                                                               1 ⚠️

Configuration ~
- ✅ OK no issues found

Runtime ~
- ✅ OK $VIMRUNTIME: C:\Program Files\Neovim\share/nvim/runtime

Performance ~
- ✅ OK Build type: Release

Remote Plugins ~
- ✅ OK Up to date

External Tools ~
- ⚠️ WARNING ripgrep not available

==============================================================================
vim.lsp:                                                                    ✅

- LSP log level : WARN
- Log path: C:/Users/<USER>/AppData/Local/nvim-data/lsp.log
- Log size: 0 KB

vim.lsp: Active Clients ~
- No active clients

vim.lsp: Enabled Configurations ~

vim.lsp: File Watcher ~
- file watching "(workspace/didChangeWatchedFiles)" disabled on all clients

vim.lsp: Position Encodings ~
- No active clients

==============================================================================
vim.provider:                                                             6 ⚠️

Clipboard (optional) ~
- ✅ OK Clipboard tool found: win32yank

Node.js provider (optional) ~
- Node.js: v22.19.0

- ⚠️ WARNING Missing "neovim" npm (or yarn, pnpm) package.
  - ADVICE:
    - Run in shell: npm install -g neovim
    - Run in shell (if you use yarn): yarn global add neovim
    - Run in shell (if you use pnpm): pnpm install -g neovim
    - You may disable this provider (and warning) by adding `let g:loaded_node_provider = 0` to your init.vim

Perl provider (optional) ~
- ⚠️ WARNING No perl executable found
  - ADVICE:
    - See :help |provider-perl| for more information.
    - You can disable this provider (and warning) by adding `let g:loaded_perl_provider = 0` to your init.vim
- ⚠️ WARNING No usable perl executable found

Python 3 provider (optional) ~
- ⚠️ WARNING No Python executable found that can `import neovim`. Using the first available executable for diagnostics.
- ⚠️ WARNING Could not load Python :
  python3 not found in search path or not executable.
  C:\ProgramData\chocolatey\bin\python3.13.EXE does not have the "neovim" module.
  python3.12 not found in search path or not executable.
  python3.11 not found in search path or not executable.
  python3.10 not found in search path or not executable.
  python3.9 not found in search path or not executable.
  C:\Python313\python.EXE does not have the "neovim" module.
  - ADVICE:
    - See :help |provider-python| for more information.
    - You can disable this provider (and warning) by adding `let g:loaded_python3_provider = 0` to your init.vim
- Executable: Not found

Python virtualenv ~
- ✅ OK no $VIRTUAL_ENV

Ruby provider (optional) ~
- ⚠️ WARNING `ruby` and `gem` must be in $PATH.
  - ADVICE:
    - Install Ruby and verify that `ruby` and `gem` commands work.

==============================================================================
vim.treesitter:                                                             ✅

Treesitter features ~
- Treesitter ABI support: min 13, max 15
- WASM parser support: false

Treesitter parsers ~
- ✅ OK Parser: c                         ABI: 15, path: C:\Program Files\Neovim\lib\nvim\parser\c.dll
- ✅ OK Parser: lua                       ABI: 15, path: C:\Program Files\Neovim\lib\nvim\parser\lua.dll
- ✅ OK Parser: markdown                  ABI: 15, path: C:\Program Files\Neovim\lib\nvim\parser\markdown.dll
- ✅ OK Parser: markdown_inline           ABI: 15, path: C:\Program Files\Neovim\lib\nvim\parser\markdown_inline.dll
- ✅ OK Parser: query                     ABI: 15, path: C:\Program Files\Neovim\lib\nvim\parser\query.dll
- ✅ OK Parser: vim                       ABI: 15, path: C:\Program Files\Neovim\lib\nvim\parser\vim.dll
- ✅ OK Parser: vimdoc                    ABI: 15, path: C:\Program Files\Neovim\lib\nvim\parser\vimdoc.dll

==============================================================================
which-key:                                                                4 ⚠️

- ✅ OK Most of these checks are for informational purposes only.
  WARNINGS should be treated as a warning, and don't necessarily indicate a problem with your config.
  Please |DON'T| report these warnings as an issue.

Checking your config ~
- ⚠️ WARNING |mini.icons| is not installed
- ⚠️ WARNING |nvim-web-devicons| is not installed
- ⚠️ WARNING Keymap icon support will be limited.

Checking for issues with your mappings ~
- ✅ OK No issues reported

checking for overlapping keymaps ~
- ⚠️ WARNING In mode `n`, <gc> overlaps with <gcc>:
  - <gc>: Toggle comment
  - <gcc>: Toggle comment line
- ✅ OK Overlapping keymaps are only reported for informational purposes.
  This doesn't necessarily mean there is a problem with your config.

Checking for duplicate mappings ~
- ✅ OK No duplicate mappings found

